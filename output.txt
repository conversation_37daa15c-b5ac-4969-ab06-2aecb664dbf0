2025-06-28 09:42:19,917 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-28 09:42:19,918 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-28 09:42:19,918 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-28 09:42:19,919 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-06-28 09:42:19,919 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-06-28 09:42:19,919 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-28 09:42:19,920 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-06-28 09:42:19,920 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-06-28 09:42:19,921 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-06-28 09:42:19,921 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-06-28 09:42:19,921 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-06-28 09:42:19,922 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-06-28 09:42:19,922 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-06-28 09:42:21,983 - __main__ - INFO - Existing processes terminated
2025-06-28 09:42:23,442 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-06-28 09:42:23,490 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-06-28 09:42:24,058 - app - INFO - Using directories from config.py:
2025-06-28 09:42:24,058 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-28 09:42:24,058 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-28 09:42:24,058 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-06-28 09:42:24,062] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-28 09:42:24,063] INFO in database: Test_steps table schema updated successfully
[2025-06-28 09:42:24,063] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 09:42:24,064] INFO in database: Screenshots table schema updated successfully
[2025-06-28 09:42:24,064] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 09:42:24,065] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 09:42:24,065] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 09:42:24,065] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 09:42:24,065] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 09:42:24,065] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 09:42:24,065] INFO in database: Database initialized successfully
[2025-06-28 09:42:24,065] INFO in database: Checking initial database state...
[2025-06-28 09:42:24,084] INFO in database: Database state: 0 suites, 0 cases, 8766 steps, 1 screenshots, 0 tracking entries
[2025-06-28 09:42:24,084] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-28 09:42:24,085] INFO in database: Test_steps table schema updated successfully
[2025-06-28 09:42:24,085] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 09:42:24,086] INFO in database: Screenshots table schema updated successfully
[2025-06-28 09:42:24,086] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 09:42:24,086] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 09:42:24,086] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 09:42:24,086] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 09:42:24,086] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 09:42:24,086] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 09:42:24,086] INFO in database: Database initialized successfully
[2025-06-28 09:42:24,086] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-28 09:42:24,087] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-28 09:42:24,087] INFO in database: action_type column already exists in execution_tracking table
[2025-06-28 09:42:24,087] INFO in database: action_params column already exists in execution_tracking table
[2025-06-28 09:42:24,087] INFO in database: action_id column already exists in execution_tracking table
[2025-06-28 09:42:24,087] INFO in database: Successfully updated execution_tracking table schema
[2025-06-28 09:42:24,087] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-28 09:42:24,088] INFO in database: Screenshots table schema updated successfully
[2025-06-28 09:42:24,088] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[2025-06-28 09:42:24,088] INFO in database: Found 0 records in execution_tracking table before clearing
[2025-06-28 09:42:24,090] INFO in database: Successfully cleared execution_tracking table. Removed 0 records.
[2025-06-28 09:42:24,194] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-28 09:42:24,195] INFO in global_values_db: Global values database initialized successfully
[2025-06-28 09:42:24,195] INFO in global_values_db: Using global values from config.py
[2025-06-28 09:42:24,195] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-28 09:42:24,255] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-28 09:42:24,269] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x119c6dd30>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-06-28 09:42:24,269] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-06-28 09:42:24,301] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-06-28 09:42:24,333] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-06-28 09:42:26,339] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-06-28 09:42:26,339] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-06-28 09:42:27,681] INFO in appium_device_controller: Installed Appium drivers: 
[2025-06-28 09:42:27,682] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-06-28 09:42:28,521] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-06-28 09:42:28,522] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-06-28 09:42:28,522] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-06-28 09:42:28,528] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
